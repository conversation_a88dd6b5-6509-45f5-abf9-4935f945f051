// 实名认证页面逻辑
const app = getApp();
const commApi = require('../../../api/commApi.js');
const userApi = require('../../../api/userApi.js');
const util = require('../../../utils/util.js');

Page({
  data: {
    // 当前步骤
    currentStep: 1,

    // 用户类型：resident(实名) 或 property(物业员工)
    userType: 'resident',

    // 认证方式：resident(实名认证) 或 property(物业员工认证)
    authMethod: 'resident',

    // 页面模式：auth(认证) 或 edit(修改)
    pageMode: 'auth',

    // 认证类型限制：resident(只显示实名认证) 或 property(只显示物业员工认证) 或 all(显示全部)
    authTypeLimit: 'all',

    // 是否显示实名认证选项
    showResidentAuth: true,

    // 是否显示物业员工认证选项
    showPropertyAuth: true,

    // 编辑模式相关状态
    originalName: '', // 原始姓名
    originalPhone: '', // 原始手机号
    hasFieldChanged: false, // 是否有字段发生变化
    needVerifyCode: false, // 是否需要验证码
    canSubmitEdit: false, // 是否可以提交修改

    // 实名表单数据
    name: '',
    phone: '',
    verifyCode: '',
    idCard: '',
    residentPhotoPath: '',
    fileUploadPath: '',//服务器文件路径
    appointmentTime: '',
    appointmentRemark: '',

    // 物业员工表单数据
    employeeId: '',
    department: '',
    departmentIndex: -1,
    position: '',
    positionIndex: -1,
    workCardPhotoPath: '',
    workCardUploadPath: '',
    certificateType: '', // 证件类型nameEn
    certificateTypeIndex: -1, // 证件类型选择器索引



    // 表单验证状态
    nameValid: false,
    nameError: false,
    phoneValid: false,
    phoneError: false,
    verifyCodeValid: false,
    verifyCodeError: false,
    idCardValid: false,
    idCardError: false,
    employeeIdValid: false,
    employeeIdError: false,
    departmentValid: false,
    departmentError: false,
    positionValid: false,
    positionError: false,
    appointmentError: false,
    certificateTypeValid: false,
    certificateTypeError: false,

    // 验证码状态
    codeSent: false,
    countDown: 60,
    codeKey: '',

    // 选项数据
    departments: [],
    positions: [],
    certificateTypes: [], // 证件类型字典数据

    // 部门选择相关
    filteredDepartments: [],
    departmentSearchKeyword: '',
    selectedDepartmentId: '',
    selectedDepartmentName: '',
    selectedDepartmentType: '', // 选中项的类型
    // expandedOrgs: [], // 不再需要，使用每个组织的expanded属性

    // 职位选择相关
    filteredPositions: [],
    positionSearchKeyword: '',
    selectedPositionId: '',
    selectedPositionName: '',
    positionLoading: false,
    positionPageNum: 1,
    positionPageSize: 10,
    positionHasMore: true,



    // 提交状态
    submitting: false,

    // 弹窗状态
    showSuccessDialogFlag: false,
    showAppointmentSelectorFlag: false,
    showCancelAuthDialog: false,
    // showCertificateTypePicker: false, // 不再需要，改用picker组件
    showDepartmentPicker: false,
    showPositionPicker: false,

    // 成功弹窗内容
    successTitle: '',
    successMessage: '',

    // 预约时间选择
    availableDates: [],
    timeSlots: [],
    selectedDateIndex: 0,
    selectedTimeSlot: null,


    isChecked: false,
    hasAuth: false
  },

  onLoad: function (options) {
  
    // 获取URL参数
    const authType = options.authType || 'all'; // resident, property, all
    const pageMode = options.mode || 'auth'; // auth, edit
    const hasAuth = options.hasAuth === 'true';

    // 根据authType设置显示选项
    let showResidentAuth = true;
    let showPropertyAuth = true;
    let defaultAuthMethod = 'resident';

    if (authType === 'resident') {
      // 只显示实名认证
      showResidentAuth = true;
      showPropertyAuth = false;
      defaultAuthMethod = 'resident';
    } else if (authType === 'property') {
      // 只显示物业员工认证
      showResidentAuth = false;
      showPropertyAuth = true;
      defaultAuthMethod = 'property';

      // 加载部门数据
      this.getOrgList();

    }

    this.setData({
      authTypeLimit: authType,
      pageMode: pageMode,
      showResidentAuth: showResidentAuth,
      showPropertyAuth: showPropertyAuth,
      authMethod: defaultAuthMethod,
      userType: defaultAuthMethod,
      hasAuth: hasAuth
    });

    // 设置导航栏标题
    this.updateNavigationTitle(defaultAuthMethod);

    // 加载证件类型字典
    this.loadCertificateTypes();

    // 如果是编辑模式，预填充用户信息
    if (pageMode === 'edit' && hasAuth) {
      this.loadUserAuthInfo();
    }

    // 初始化预约日期数据
    this.initAppointmentDates();

    console.log('页面加载完成，认证类型限制：', authType, '页面模式：', pageMode);
  },

  // 加载证件类型字典
  loadCertificateTypes: function () {
    try {
      const certificateTypeDict = util.getDictByNameEn('certificate_type');
      if (certificateTypeDict && certificateTypeDict.length > 0 && certificateTypeDict[0].children) {
        this.setData({
          certificateTypes: certificateTypeDict[0].children
        });
      } else {
        // 使用默认数据
        this.setData({
          certificateTypes: [
            { nameCn: '身份证', nameEn: 'id_card' },
            { nameCn: '护照', nameEn: 'passport' },
            { nameCn: '港澳通行证', nameEn: 'hk_macao_pass' },
            { nameCn: '台湾通行证', nameEn: 'taiwan_pass' }
          ]
        });
      }
      console.log('证件类型字典加载完成:', this.data.certificateTypes);

      // 如果已有证件类型数据，设置选择器索引
      this.setCertificateTypeIndex();

      // 如果已有证件类型数据，设置选择器索引
      this.setCertificateTypeIndex();
    } catch (error) {
      console.error('加载证件类型字典失败:', error);
      // 使用默认数据
      this.setData({
        certificateTypes: [
          { nameCn: '身份证', nameEn: 'id_card' },
          { nameCn: '护照', nameEn: 'passport' },
          { nameCn: '港澳通行证', nameEn: 'hk_macao_pass' },
          { nameCn: '台湾通行证', nameEn: 'taiwan_pass' }
        ]
      });
    }
  },

  // 设置证件类型选择器索引
  setCertificateTypeIndex: function() {
    if (this.data.certificateType && this.data.certificateTypes.length > 0) {
      const certificateTypeIndex = this.data.certificateTypes.findIndex(item => item.nameEn === this.data.certificateType);
      this.setData({ certificateTypeIndex: certificateTypeIndex >= 0 ? certificateTypeIndex : -1 });
      console.log('设置证件类型索引:', { certificateType: this.data.certificateType, index: certificateTypeIndex });
    }
  },

  // 加载用户认证信息（编辑模式时使用）
  loadUserAuthInfo: function () {
    const userInfo = wx.getStorageSync('userInfo') || {};

    if (userInfo) {
      const name = userInfo.realName || '';
      const phone = userInfo.phone || '';
      const idCard = userInfo.idCardNumber || '';
      const certificateType = userInfo.certificateType || '';

      this.setData({
        name: name,
        phone: phone,
        idCard: idCard,
        certificateType: certificateType,
        // 保存原始数据用于比较
        originalName: name,
        originalPhone: phone,
        hasFieldChanged: false,
        needVerifyCode: false,
        canSubmitEdit: false
      });

      // 设置证件类型选择器索引
      this.setCertificateTypeIndex();

      // 验证预填充的字段
      if (name) this.validateName();
      if (phone) this.validatePhone();
      if (idCard) this.validateIdCard();
      if (certificateType) this.validateCertificateType();
    }
  },

  // 检查字段是否发生变化（编辑模式）
  checkFieldChanges: function () {
    const nameChanged = this.data.name !== this.data.originalName;
    const phoneChanged = this.data.phone !== this.data.originalPhone;
    const hasChanged = nameChanged || phoneChanged;

    // 如果姓名或手机号发生变化，需要验证码
    const needVerifyCode = hasChanged;

    this.setData({
      hasFieldChanged: hasChanged,
      needVerifyCode: needVerifyCode,
      canSubmitEdit: hasChanged && !needVerifyCode // 如果有变化但不需要验证码，或者验证码已验证
    });

    // 如果需要验证码但验证码已验证，则可以提交
    if (needVerifyCode && this.data.verifyCodeValid) {
      this.setData({ canSubmitEdit: true });
    }

    console.log('字段变化检测：', {
      nameChanged,
      phoneChanged,
      hasChanged,
      needVerifyCode,
      canSubmitEdit: this.data.canSubmitEdit
    });
  },

  // 验证表单
  validateForm: function () {
    // 验证姓名
    if (!this.validateName()) {
      wx.showToast({
        title: '请输入正确的姓名',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号
    if (!this.validatePhone()) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }

    // 如果是编辑模式且需要验证码，验证验证码
    if (this.data.pageMode === 'edit' && this.data.needVerifyCode) {
      if (!this.validateVerifyCode()) {
        wx.showToast({
          title: '请输入正确的验证码',
          icon: 'none'
        });
        return false;
      }
    }

    // 如果是新用户认证，验证验证码
    if (this.data.pageMode === 'auth' && !this.data.hasAuth) {
      if (!this.validateVerifyCode()) {
        wx.showToast({
          title: '请输入正确的验证码',
          icon: 'none'
        });
        return false;
      }
    }

    // 如果是物业员工认证，验证额外字段
    if (this.data.authMethod === 'property') {
      if (!this.validateCertificateType()) {
        wx.showToast({
          title: '请选择证件类型',
          icon: 'none'
        });
        return false;
      }

      if (!this.validateEmployeeId()) {
        wx.showToast({
          title: '请输入正确的员工编号',
          icon: 'none'
        });
        return false;
      }

      if (!this.data.department) {
        wx.showToast({
          title: '请选择所属部门',
          icon: 'none'
        });
        return false;
      }

      if (!this.data.position) {
        wx.showToast({
          title: '请选择职位',
          icon: 'none'
        });
        return false;
      }

      if (!this.data.workCardPhotoPath) {
        wx.showToast({
          title: '请上传工作证照片',
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },



  // 初始化预约日期数据
  initAppointmentDates: function () {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const availableDates = [];

    // 生成未来7天的日期
    const now = new Date();
    for (let i = 1; i <= 7; i++) {
      const date = new Date(now);
      date.setDate(now.getDate() + i);

      // 跳过周末
      if (date.getDay() === 0 || date.getDay() === 6) {
        continue;
      }

      availableDates.push({
        date: date.toISOString().split('T')[0],
        day: date.getDate(),
        month: date.getMonth() + 1,
        weekday: weekdays[date.getDay()]
      });
    }

    // 初始化时间段
    const timeSlots = [
      { time: '09:00-10:00', available: true },
      { time: '10:00-11:00', available: true },
      { time: '11:00-12:00', available: true },
      { time: '14:00-15:00', available: true },
      { time: '15:00-16:00', available: true },
      { time: '16:00-17:00', available: true }
    ];

    // 随机设置一些时间段为不可用
    timeSlots.forEach((_, index) => {
      if (Math.random() < 0.3) {
        timeSlots[index].available = false;
      }
    });

    this.setData({
      availableDates,
      timeSlots
    });
  },

  // 切换用户类型
  switchUserType: function (e) {
    const type = e.currentTarget.dataset.type || e.target.dataset.type;
    if (type && type !== this.data.userType) {
      this.setData({ userType: type });
    }
  },

  // 切换认证方式
  switchAuthMethod: function (e) {
    const method = e.currentTarget.dataset.method;
    if (method && method !== this.data.authMethod) {
      this.setData({ authMethod: method });
      // 更新导航栏标题
      this.updateNavigationTitle(method);
    }
  },

  // 更新导航栏标题
  updateNavigationTitle: function (authMethod) {
    let title = '实名认证';
    if (authMethod === 'property') {
      title = '物业员工认证';
    }

    wx.setNavigationBarTitle({
      title: title
    });
  },

  // 返回上一页
  goBack: function () {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 跳转到隐私政策页面
  goToPrivacyPolicy: function () {
    wx.navigateTo({
      url: '/pages/settings/privacy/privacy'
    });
  },

  // 实名表单验证
  onNameInput: function (e) {
    this.setData({ name: e.detail.value });
    this.validateName();
    // 检查字段变化（编辑模式）
    if (this.data.pageMode === 'edit') {
      this.checkFieldChanges();
    }
  },

  onPhoneInput: function (e) {
    this.setData({ phone: e.detail.value });
    this.validatePhone();
    // 检查字段变化（编辑模式）
    if (this.data.pageMode === 'edit') {
      this.checkFieldChanges();
    }
  },

  onVerifyCodeInput: function (e) {
    this.setData({ verifyCode: e.detail.value });
    const isValid = this.validateVerifyCode();

    // 编辑模式下，验证码验证通过后允许提交
    if (this.data.pageMode === 'edit' && this.data.needVerifyCode && isValid) {
      this.setData({ canSubmitEdit: true });
    }
  },

  onIdCardInput: function (e) {
    this.setData({ idCard: e.detail.value });
    this.validateIdCard();
  },

  onEmployeeIdInput: function (e) {
    this.setData({ employeeId: e.detail.value });
    this.validateEmployeeId();
  },

  onAppointmentRemarkInput: function (e) {
    this.setData({ appointmentRemark: e.detail.value });
  },

  validateName: function () {
    const name = this.data.name.trim();
    const isValid = name.length >= 2;
    this.setData({
      nameValid: isValid,
      nameError: name.length > 0 && !isValid
    });
    return isValid;
  },

  validatePhone: function () {
    const util = require('@/utils/util');
    const phone = this.data.phone.trim();
    const isValid = util.validatePhone(phone);
    this.setData({
      phoneValid: isValid,
      phoneError: phone.length > 0 && !isValid
    });
    return isValid;
  },

  validateVerifyCode: function () {
    const verifyCode = this.data.verifyCode.trim();
    const isValid = /^\d{6}$/.test(verifyCode);
    this.setData({
      verifyCodeValid: isValid,
      verifyCodeError: verifyCode.length > 0 && !isValid
    });
    return isValid;
  },

  validateIdCard: function () {
    const idCard = this.data.idCard.trim();
    let isValid = false;

    if (idCard.length === 0) {
      isValid = false;
    } else {
      // 根据证件类型进行不同的验证
      const certificateType = this.data.certificateType;

      switch (certificateType) {
        case 'id_card':
          // 身份证：18位数字，最后一位可能是X
          isValid = /^\d{17}[\dXx]$/.test(idCard);
          break;
        case 'passport':
          // 护照：6-20位字母数字组合
          isValid = /^[A-Za-z0-9]{6,20}$/.test(idCard);
          break;
        case 'hk_macao_pass':
        case 'taiwan_pass':
          // 港澳通行证/台湾通行证：8-10位字母数字组合
          isValid = /^[A-Za-z0-9]{8,10}$/.test(idCard);
          break;
        default:
          // 默认验证：至少6位字符
          isValid = idCard.length >= 6;
          break;
      }
    }

    this.setData({
      idCardValid: isValid,
      idCardError: idCard.length > 0 && !isValid
    });
    return isValid;
  },

  validateEmployeeId: function () {
    const employeeId = this.data.employeeId.trim();
    // 员工编号应该是3-20位的字母数字组合
    const isValid = employeeId.length >= 3 && employeeId.length <= 20 && /^[A-Za-z0-9]+$/.test(employeeId);
    this.setData({
      employeeIdValid: isValid,
      employeeIdError: employeeId.length > 0 && !isValid
    });
    return isValid;
  },

  // 发送验证码
  sendVerifyCode: function () {
    if (!this.validatePhone()) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    // 显示发送中提示
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    // 使用新的API发送验证码
    commApi.getSmsCode(this.data.phone)
      .then((res) => {
        wx.hideLoading();


        // 设置验证码已发送状态
        this.setData({
          codeSent: true,
          codeKey: res
        });

        // 开始倒计时
        this.startCountDown();

        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        });


      })
      .catch(err => {
        wx.hideLoading();
        console.log('发送验证码失败', err);

        this.setData({
          codeSent: false
        });

        wx.showToast({
          title: '验证码发送失败' + (err.errorMessage ? (',' + err.errorMessage) : ''),
          icon: 'error'
        });
      });
  },

  // 开始倒计时
  startCountDown: function () {
    const countDownTimer = setInterval(() => {
      if (this.data.countDown <= 1) {
        clearInterval(countDownTimer);
        this.setData({
          codeSent: false,
          countDown: 60
        });
      } else {
        this.setData({
          countDown: this.data.countDown - 1
        });
      }
    }, 1000);
  },







  // 从相册选择照片
  chooseFromAlbum: function () {
    console.log('调用从相册选择照片方法');
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 尝试使用旧的API，可能在某些设备上更稳定
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        console.log('选择图片成功', res);
        this.setData({ residentPhotoPath: res.tempFilePaths[0] });
        //上传图片
        this.uploadPhoto(res.tempFilePaths[0])

        wx.hideLoading();
      },
      fail: (err) => {
        console.log('选择图片失败(旧API)', err);
        // 如果旧API失败，尝试使用新API
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album'],
          sizeType: ['compressed'],
          success: (res) => {
            console.log('选择图片成功(新API)', res);
            // 检查文件大小，限制为5MB
            const tempFile = res.tempFiles[0];
            const fileSizeInMB = tempFile.size / (1024 * 1024);

            if (fileSizeInMB > 5) {
              wx.hideLoading();
              wx.showToast({
                title: '图片大小不能超过5MB',
                icon: 'none',
                duration: 2000
              });
              return;
            }

            this.setData({ residentPhotoPath: tempFile.tempFilePath });
            wx.hideLoading();
          },
          fail: (err2) => {
            console.log('选择图片失败(新API)', err2);
            // 用户取消选择不显示错误提示
            if (err2.errMsg !== "chooseMedia:fail cancel") {
              wx.showToast({
                title: '选择图片失败',
                icon: 'none',
                duration: 2000
              });
            }
            wx.hideLoading();
          }
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 拍照上传 - 简化版，只使用一种API
  takePhoto: function () {
    console.log('调用拍照上传方法 - 简化版');
    wx.showLoading({
      title: '准备相机...',
      mask: true
    });

    // 直接使用wx.chooseImage，这是最基础的API，兼容性最好
    wx.chooseImage({
      count: 1, // 最多可以选择的图片张数
      sizeType: ['compressed'], // 所选的图片的尺寸压缩模式
      sourceType: ['camera'], // 设置只允许使用相机，强制直接打开相机
      success: (res) => {
        console.log('拍照成功', res);
        // 设置照片路径
        this.setData({ residentPhotoPath: res.tempFilePaths[0] });

        //上传图片（本地测试环境为非必填项）
        this.uploadPhoto(res.tempFilePaths[0]);

        wx.hideLoading();
      },
      fail: (err) => {
        console.log('拍照失败', err);
        // 用户取消拍照不显示错误提示
        if (err.errMsg !== "chooseImage:fail cancel") {
          wx.showToast({
            title: '拍照失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
        wx.hideLoading();
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 删除照片
  removePhoto: function () {
    this.setData({
      residentPhotoPath: '',
      fileUploadPath: ''
    });
  },

  // 显示部门选择器
  showDepartmentPicker: function () {
    wx.showActionSheet({
      itemList: this.data.departments,
      success: (res) => {
        this.setData({
          department: this.data.departments[res.tapIndex],
          departmentIndex: res.tapIndex,
          departmentValid: true,
          departmentError: false
        });
      }
    });
  },

  // 显示职位选择器
  showPositionPicker: function () {
    wx.showActionSheet({
      itemList: this.data.positions,
      success: (res) => {
        this.setData({
          position: this.data.positions[res.tapIndex],
          positionIndex: res.tapIndex,
          positionValid: true,
          positionError: false
        });
      }
    });
  },

  // 选择工作证照片
  chooseWorkCardPhoto: function () {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({ workCardPhotoPath: res.tempFilePaths[0] });
        // 上传工作证照片
        this.uploadWorkCardPhoto(res.tempFilePaths[0]);
      }
    });
  },

  // 删除工作证照片
  removeWorkCardPhoto: function () {
    this.setData({
      workCardPhotoPath: '',
      workCardUploadPath: ''
    });
  },

  // 上传工作证照片
  uploadWorkCardPhoto: function (src) {
    if (!src) {
      console.log('本地测试环境，工作证照片上传已跳过');
      return Promise.resolve({ data: '', message: '本地测试环境，工作证照片上传已跳过' });
    }

    return commApi.upLoadFile(src)
      .then((result) => {
        console.log('工作证照片上传成功', result);
        this.setData({
          workCardUploadPath: result.data || ''
        });
        wx.showToast({
          title: '工作证照片上传成功',
          icon: 'success',
          duration: 1500
        });
        return result;
      })
      .catch((errorMsg) => {
        console.log('工作证照片上传失败', errorMsg);
        console.log('本地测试环境，工作证照片上传失败已忽略');
        return { data: '', message: '本地测试环境，工作证照片上传失败已忽略' };
      });
  },


  uploadPhoto(src) {
    if (!src) {
      console.log('本地测试环境，图片上传已跳过');
      return Promise.resolve({ data: '', message: '本地测试环境，图片上传已跳过' });
    }

    return commApi.upLoadFile(src)
      .then((result) => {
        console.log('图片上传成功', result);

        this.setData({
          fileUploadPath: result.data || ''
        });

        wx.showToast({
          title: '图片上传成功',
          icon: 'success',
          duration: 1500
        });

        return result;
      })
      .catch((errorMsg) => {
        console.log('图片上传失败', errorMsg);

        // 本地测试环境，不显示错误提示
        console.log('本地测试环境，图片上传失败已忽略');

        return { data: '', message: '本地测试环境，图片上传失败已忽略' };
      });
  },



  // 保留原方法用于兼容性
  chooseResidentPhoto: function () {
    this.chooseFromAlbum();
  },



  // 提交表单
  submitForm: function () {

    if (this.data.authMethod === 'resident') {
      this.submitResidentForm();
    } else if (this.data.authMethod === 'property') {
      this.submitPropertyForm();
    }
  },

  // 预约时间选择器
  /**
   * 显示预约选择器
   */
  showAppointmentSelector: function () {
    this.setData({ showAppointmentSelectorFlag: true });
  },

  closeAppointmentSelector: function () {
    this.setData({ showAppointmentSelectorFlag: false });
  },

  selectDate: function (e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      selectedDateIndex: index,
      selectedTimeSlot: null
    });
  },

  selectTimeSlot: function (e) {
    const index = e.currentTarget.dataset.index;
    const available = e.currentTarget.dataset.available;

    if (!available) {
      return;
    }

    this.setData({ selectedTimeSlot: index });
  },

  confirmAppointment: function () {
    if (this.data.selectedTimeSlot === null) {
      return;
    }

    const selectedDate = this.data.availableDates[this.data.selectedDateIndex];
    const selectedTime = this.data.timeSlots[this.data.selectedTimeSlot];

    const appointmentTime = `${selectedDate.month}月${selectedDate.day}日 ${selectedDate.weekday} ${selectedTime.time}`;

    this.setData({
      appointmentTime: appointmentTime,
      appointmentError: false,
      showAppointmentSelectorFlag: false
    });
  },

  // 提交实名认证表单
  submitResidentForm: function () {
    console.log("开始提交实名认证表单");

    // 验证表单
    let isValid = true;

    // 验证姓名
    if (!this.validateName()) {
      isValid = false;
    }

    // 验证手机号
    if (!this.validatePhone()) {
      isValid = false;
    }

    // 验证验证码（如果不是已认证用户）
    if (!this.data.hasAuth && !this.validateVerifyCode()) {
      isValid = false;
    }

    // 验证身份证号
    // if (!this.validateIdCard()) {
    //   isValid = false;
    // }

    // // 根据认证方式验证不同的字段
    // if (this.data.authMethod === 'online') {
    //   // 线上认证，图片上传为非必填项（本地测试环境）
    //   console.log('线上认证，图片上传为非必填项（本地测试环境）');
    // } else {
    //   // 线下认证需要验证预约时间
    //   if (!this.data.appointmentTime) {
    //     this.setData({ appointmentError: true });
    //     wx.showToast({
    //       title: '请选择预约时间',
    //       icon: 'none'
    //     });
    //     isValid = false;
    //   }
    // }

    if (isValid) {
      console.log("表单验证通过，开始保存数据");

      // 显示加载状态
      this.setData({ submitting: true });

      // 准备认证数据
      const authData = {
        realName: this.data.name,
        phone: this.data.phone,
        codeKey: this.data.codeKey,
        code: this.data.verifyCode
      };

      // 调用实名认证API
      userApi.submitRealNameAuth(authData)
        .then(res => {


          // this.showSuccessDialog('认证信息提交成功');
          this.handleAuthSuccess();



        })
        .catch(err => {

          console.log('实名认证提交失败', err);
          // 本地测试环境，模拟成功
          wx.showToast({
            title: '实名认证失败' + (err.errorMessage ? (',' + err.errorMessage) : ''),
            icon: 'error',
            duration: 1500
          });

        });
    } else {

      console.log("表单验证失败");
      wx.showToast({
        title: '请填写正确的信息',
        icon: 'none'
      });
    }
  },

  // 提交物业员工认证表单
  submitPropertyForm: function () {
    console.log("开始提交物业员工认证表单");

    // 验证表单 - 逐个验证并显示具体错误信息
    const validationErrors = [];

    // 验证姓名
    if (!this.validateName()) {
      validationErrors.push('请输入正确的姓名');
    }

    // 验证手机号
    if (!this.validatePhone()) {
      validationErrors.push('请输入正确的手机号');
    }

    // 验证证件类型
    if (!this.validateCertificateType()) {
      validationErrors.push('请选择证件类型');
    }

    // 验证证件号码
    if (!this.validateIdCard()) {
      validationErrors.push('请输入正确的证件号码');
    }

    // 验证员工编号
    if (!this.validateEmployeeId()) {
      validationErrors.push('请输入正确的员工编号');
    }

    // 验证部门
    if (!this.validateDepartment()) {
      validationErrors.push('请选择所属部门');
    }

    // 验证职位
    if (!this.validatePosition()) {
      validationErrors.push('请选择职位');
    }

    // 验证工作证照片（检查是否已上传到服务器）
    if (!this.data.workCardUploadPath) {
      validationErrors.push('请上传工作证照片');
    }

    // 如果有验证错误，显示第一个错误信息
    if (validationErrors.length > 0) {
      wx.showToast({
        title: validationErrors[0],
        icon: 'none',
        duration: 2000
      });
      console.log('物业员工表单验证失败:', validationErrors);
      return;
    }

    // 表单验证通过，开始提交认证
    console.log("物业员工表单验证通过，开始提交认证");

    // 显示加载状态
    this.setData({ submitting: true });

    // 直接使用已上传的工作证照片路径，不重复上传
    const authData = {
      personName: this.data.name,
      certificateType: this.data.certificateType,
      idCard: this.data.idCard,
      phone: this.data.phone,
      personNumber: this.data.employeeId,
      media: this.data.workCardUploadPath || '', // 使用已上传的路径
      orgId: this.data.departmentId || this.data.selectedDepartmentId,
      positionId: this.data.positionId || this.data.selectedPositionId
    };

    console.log('提交物业员工认证数据:', authData);

    // 直接调用物业员工认证API
    userApi.submitPropertyAuth(authData)
      .then(res => {
        console.log('物业员工认证提交成功:', res);
        this.setData({ submitting: false });

        // 处理认证成功
        this.handlePropertyAuthSuccess();
      })
      .catch(err => {
        console.error('物业员工认证提交失败:', err);
        this.setData({ submitting: false });

        wx.showToast({
          title: '认证提交失败' + (err.errorMessage ? ('，' + err.errorMessage) : ''),
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 处理认证成功
  handleAuthSuccess: function () {

    userApi.getUserInfo().then(res => {

      if (res) {

        console.log('userApi.getUserInfo()', res)
        var userInfo = res
        // 统一保存用户信息
        wx.setStorageSync('userInfo', userInfo);

        this.showSuccessDialog('实名认证提交成功');

      }
    }).catch(err => {

      console.log('实名认证提交失败', err);
      // 本地测试环境，模拟成功
      wx.showToast({
        title: '实名认证失败' + (err.errorMessage ? (',' + err.errorMessage) : ''),
        icon: 'error',
        duration: 1500
      });

    });
  },

  // 处理物业员工认证成功
  handlePropertyAuthSuccess: function () {
    var  communityId= wx.getStorageSync('selectedCommunity').id
    userApi.getProperTyInfo(communityId).then(res => {
      if (res) {
        console.log('物业员工认证成功，获取用户信息:', res);

        // 统一保存用户信息
        wx.setStorageSync('propertyInfo', res);
        wx.setStorageSync('isPropertyStaff', true);

        this.showSuccessDialog('物业员工认证提交成功');
      }else {
        wx.removeStorageSync('propertyInfo')
        wx.setStorageSync('isPropertyStaff', false);

      }
    }).catch(err => {
      console.error('获取物业认证信息失败:', err);

      
    });
  },



  // 显示成功弹窗
  showSuccessDialog: function (title, message) {
    this.setData({
      successTitle: title,
      successMessage: message,
      showSuccessDialogFlag: true
    });
  },

  closeSuccessDialog: function () {
    this.setData({ showSuccessDialogFlag: false });
  },

  confirmSuccess: function () {
    this.setData({ showSuccessDialogFlag: false });

    // 检查是否有重定向页面
    const redirectPage = wx.getStorageSync('redirectAfterAuth');

    if (redirectPage) {
      // 清除重定向信息
      wx.removeStorageSync('redirectAfterAuth');

      // 跳转到指定页面
      wx.redirectTo({
        url: redirectPage
      });
    } else {
      // 默认跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    return false;
  },

  // 显示已有认证的提示信息
  showAlreadyAuthenticatedTip: function () {

    const userInfo = wx.getStorageSync('userInfo') || {};

    //this.setData使用userInfo回填认证信息
    //     avatarUrl: null
    // birthday: "1990-06-05 23:00:00"
    // gender: "man"
    // id: "3"
    // nickName: null
    // openid: "o7nun7a9uuYlWTOSj6XlTtnz5M4U"
    // phone: "13685156020"
    // residentId: "1"
    // role: "user"
    // unionid: null
    // realName: "卜凡傲"

    this.setData({
      // hasAuth:true,
      name: userInfo.realName,
      phone: userInfo.phone,
      idCard: userInfo.idCardNumber
    })

    wx.showModal({
      title: '温馨提示',
      content: '您已完成实名身份认证。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },
  switchChange(e) {
    this.setData({
      isChecked: e.detail.value
    })
  },

  // 修改认证信息
  updateAuthInfo: function () {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    // 准备修改数据
    const updateData = {
      name: this.data.name,
      phone: this.data.phone,
      idCard: this.data.idCard,
      authMethod: this.data.authMethod
    };

    // 如果是物业员工认证，添加额外字段
    if (this.data.authMethod === 'property') {
      updateData.employeeId = this.data.employeeId;
      updateData.department = this.data.department;
      updateData.position = this.data.position;
      updateData.workCardPhoto = this.data.workCardPhotoServerPath;
    }

    console.log('修改认证信息：', updateData);

    // 模拟API调用（实际项目中需要调用真实API）
    setTimeout(() => {
      this.setData({ submitting: false });
      this.showSuccessDialog('认证信息修改成功');
    }, 2000);
  },

  // 显示注销认证确认弹窗
  showCancelAuthDialog: function () {
    this.setData({
      showCancelAuthDialog: true
    });
  },

  // 关闭注销认证弹窗
  closeCancelAuthDialog: function () {
    this.setData({
      showCancelAuthDialog: false
    });
  },

  // 确认注销认证
  confirmCancelAuth: function () {
    this.setData({
      showCancelAuthDialog: false
    });
    this.cancelAuth();
  },

  // 注销认证
  cancelAuth: function () {

    wx.showLoading({
      title: '注销中...',
      mask: true
    });

    userApi.logout().then(res => {

      wx.hideLoading();

      // 清除所有登录相关的存储
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('access_token');
      wx.removeStorageSync('refresh_token');
      wx.removeStorageSync('openid');
      wx.removeStorageSync('session_key');
      wx.removeStorageSync('memberDetail');
      wx.removeStorageSync('isPropertyStaff'); // 清除物业员工标识

      // 重置应用登录状态
      app.globalData.isAuthenticated = false;
      app.globalData.isLoginInitializing = false;
      app.globalData.loginInitPromise = null;

      console.log('已清除登录信息，开始重新初始化登录');

      // 重新初始化登录并加载数据
      this.waitForLoginAndLoadData();

    })
      .catch(err => {
        console.log('用户注销提交失败', err);
        // 本地测试环境，模拟成功
        wx.showToast({
          title: '用户注销失败' + (err.errorMessage ? (',' + err.errorMessage) : ''),
          icon: 'error',
          duration: 1500
        });

      });


  },

  waitForLoginAndLoadData() {
    app.userLogin()
      .then(result => {

        wx.showToast({
          title: '注销成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      })
      .catch(error => {

      })
  },


  getOrgList(orgName = '')
  {
    var params = {
      communityId: wx.getStorageSync('selectedCommunity').id
    }

    // 如果有搜索关键字，添加到参数中
    if (orgName) {
      params.orgName = orgName;
    }

    commApi.getOrgList(params)
    .then(res => {
      console.log('getOrgList',res)

      // 根据文档说明，res已经是处理过的data了
      if (res && Array.isArray(res)) {
        // 给每个组织添加展开状态
        const processedData = this.addExpandedState(res);

        this.setData({
          departments: processedData, // 保存原始层级数据
          filteredDepartments: processedData // 直接使用原始数据，保持层级结构
        });
        console.log('部门数据加载完成:', processedData);
      }
    })
    .catch(error => {
      console.error('加载部门数据失败:', error);
      wx.showToast({
        title: '加载部门数据失败',
        icon: 'none'
      });
    })
  },

  getPositionPage(positionName, isRefresh = false)
  {
    if (this.data.positionLoading) return;

    this.setData({ positionLoading: true });

    const pageNum = isRefresh ? 1 : this.data.positionPageNum;

    var params = {
      positionName: positionName || '',
      pageNum: pageNum,
      pageSize: this.data.positionPageSize
    }

    commApi.getPositionPage(params)
    .then(res => {
      console.log('getPositionPage', res);

      if (res && res) {
        const newPositions = res.list || [];
        const total = res.total || 0;

        let updatedPositions = [];
        if (isRefresh) {
          updatedPositions = newPositions;
        } else {
          updatedPositions = [...this.data.filteredPositions, ...newPositions];
        }

        const hasMore = updatedPositions.length < total;

        this.setData({
          filteredPositions: updatedPositions,
          positionPageNum: pageNum + 1,
          positionHasMore: hasMore,
          positionLoading: false
        });
      } else {
        this.setData({ positionLoading: false });
      }
    })
    .catch(error => {
      console.error('加载职位数据失败:', error);
    
    })
  },

  // 证件类型picker选择事件
  onCertificateTypeChange: function (e) {
    const index = e.detail.value;
    const selectedType = this.data.certificateTypes[index];

    this.setData({
      certificateTypeIndex: index,
      certificateType: selectedType ? selectedType.nameEn : ''
    });

    this.validateCertificateType();
    console.log('选择证件类型:', selectedType);
  },

  // 验证证件类型
  validateCertificateType: function () {
    const isValid = !!this.data.certificateType;
    this.setData({
      certificateTypeValid: isValid,
      certificateTypeError: !isValid
    });
    return isValid;
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 阻止事件冒泡
  },

  // 测试功能 - 开发调试用
  testFeatures: function () {
    console.log('=== 物业员工认证功能测试 ===');
    console.log('1. 证件类型数据:', this.data.certificateTypes);
    console.log('2. 部门数据:', this.data.departments);
    console.log('3. 职位数据:', this.data.filteredPositions);
    console.log('4. 当前表单数据:', {
      name: this.data.name,
      phone: this.data.phone,
      certificateType: this.data.certificateType,
      idCard: this.data.idCard,
      employeeId: this.data.employeeId,
      department: this.data.department,
      position: this.data.position,
      workCardPhotoPath: this.data.workCardPhotoPath
    });
    console.log('5. 验证状态:', {
      nameValid: this.data.nameValid,
      phoneValid: this.data.phoneValid,
      certificateTypeValid: this.data.certificateTypeValid,
      idCardValid: this.data.idCardValid,
      employeeIdValid: this.data.employeeIdValid,
      departmentValid: this.data.departmentValid,
      positionValid: this.data.positionValid
    });
    console.log('=== 测试完成 ===');
  },

  // 显示部门选择器
  showDepartmentPicker: function () {
    this.setData({
      showDepartmentPicker: true,
      departmentSearchKeyword: '',
      filteredDepartments: this.data.departments || [],
      selectedDepartmentId: this.data.departmentId || '',
      selectedDepartmentName: '',
      selectedDepartmentType: '', // 记录选中项的类型
      // expandedOrgs: [] // 不再需要，使用每个组织的expanded属性
    });

    // 如果还没有部门数据，重新加载
    if (!this.data.departments || this.data.departments.length === 0) {
      this.getOrgList();
    }
  },

  // 关闭部门选择器
  closeDepartmentPicker: function () {
    this.setData({
      showDepartmentPicker: false
    });
  },

  // 部门搜索输入
  onDepartmentSearchInput: function (e) {
    const keyword = e.detail.value.trim();
    this.setData({
      departmentSearchKeyword: keyword
    });

    // 使用API搜索
    this.searchDepartments(keyword);
  },

  // 搜索部门（调用API）
  searchDepartments: function (keyword) {
    // 重新调用API进行搜索
    this.getOrgList(keyword);
  },

  // 选择部门
  selectDepartment: function (e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;
    const type = e.currentTarget.dataset.type;
    const level = e.currentTarget.dataset.level;

    console.log('点击了组织:', { id, name, type, level });
    console.log('当前filteredDepartments:', this.data.filteredDepartments);

    if (type === 'company') {
      // 点击的是公司，切换展开状态
      console.log(`准备切换公司 ${name}(${id}) 的展开状态`);
      this.toggleCompanyExpanded(id);

      // 公司不能被选中，清除选择状态
      this.setData({
        selectedDepartmentId: '',
        selectedDepartmentName: '',
        selectedDepartmentType: ''
      });

    } else if (type === 'dept') {
      // 点击的是部门，可以选择
      this.setData({
        selectedDepartmentId: id,
        selectedDepartmentName: name,
        selectedDepartmentType: type
      });

      console.log('选择了部门:', { id, name, type });
    }
  },

  // 切换公司展开状态
  toggleCompanyExpanded: function(companyId) {
    console.log(`开始切换公司 ${companyId} 的展开状态`);

    const updateExpanded = (orgList, depth = 0) => {
      console.log(`处理层级 ${depth}:`, orgList.map(org => ({ id: org.id, name: org.orgName, expanded: org.expanded, hasChildren: !!(org.children && org.children.length) })));

      return orgList.map(org => {
        if (org.id === companyId && org.type === 'company') {
          // 找到目标公司，切换展开状态
          const newExpanded = !org.expanded;
          console.log(`找到目标公司 ${org.orgName}(${org.id}), 展开状态: ${org.expanded} -> ${newExpanded}`);
          console.log(`该公司的children:`, org.children);
          return {
            ...org,
            expanded: newExpanded
          };
        }

        // 递归处理子级
        if (org.children && org.children.length) {
          return {
            ...org,
            children: updateExpanded(org.children, depth + 1)
          };
        }

        return org;
      });
    };

    const updatedDepartments = updateExpanded(this.data.filteredDepartments);
    console.log('更新后的数据:', updatedDepartments);

    this.setData({
      filteredDepartments: updatedDepartments
    });
  },

  // 给组织数据添加展开状态
  addExpandedState: function(orgList) {
    return orgList.map(org => {
      const processedOrg = {
        ...org,
        expanded: false // 默认收起状态
      };

      // 递归处理子级
      if (org.children && org.children.length) {
        processedOrg.children = this.addExpandedState(org.children);
      }

      return processedOrg;
    });
  },

  // 旧的方法已删除，使用新的toggleCompanyExpanded方法

  // 确认部门选择
  confirmDepartment: function () {
    // 只有选择了部门类型的数据才能确认
    if (this.data.selectedDepartmentId && this.data.selectedDepartmentType === 'dept') {
      this.setData({
        department: this.data.selectedDepartmentName,
        departmentId: this.data.selectedDepartmentId,
        showDepartmentPicker: false
      });

      // 验证部门选择
      this.validateDepartment();
    }
  },

  // 验证部门选择
  validateDepartment: function () {
    const isValid = !!this.data.department;
    this.setData({
      departmentValid: isValid,
      departmentError: !isValid
    });
    return isValid;
  },

  // 显示职位选择器
  showPositionPicker: function () {
    this.setData({
      showPositionPicker: true,
      positionSearchKeyword: '',
      filteredPositions: [],
      positionPageNum: 1,
      positionHasMore: true,
      selectedPositionId: this.data.positionId || '',
      selectedPositionName: ''
    });

    // 加载职位数据
    this.getPositionPage('', true);
  },

  // 关闭职位选择器
  closePositionPicker: function () {
    this.setData({
      showPositionPicker: false
    });
  },

  // 职位搜索输入
  onPositionSearchInput: function (e) {
    const keyword = e.detail.value.trim();
    this.setData({
      positionSearchKeyword: keyword,
      filteredPositions: [],
      positionPageNum: 1,
      positionHasMore: true
    });

    // 重新搜索职位
    this.getPositionPage(keyword, true);
  },

  // 加载更多职位
  loadMorePositions: function () {
    if (this.data.positionHasMore && !this.data.positionLoading) {
      this.getPositionPage(this.data.positionSearchKeyword);
    }
  },

  // 选择职位
  selectPosition: function (e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;

    this.setData({
      selectedPositionId: id,
      selectedPositionName: name
    });
  },

  // 确认职位选择
  confirmPosition: function () {
    if (this.data.selectedPositionId) {
      this.setData({
        position: this.data.selectedPositionName,
        positionId: this.data.selectedPositionId,
        showPositionPicker: false
      });

      // 验证职位选择
      this.validatePosition();
    }
  },

  // 验证职位选择
  validatePosition: function () {
    const isValid = !!this.data.position;
    this.setData({
      positionValid: isValid,
      positionError: !isValid
    });
    return isValid;
  }

});
